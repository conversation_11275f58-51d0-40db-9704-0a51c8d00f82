<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>修复验证</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
    </style>
    
    <!-- 工具库加载函数 -->
    <script>
        function loadScript(src, callback) {
            const script = document.createElement('script');
            script.src = src;
            script.onload = callback || function() {};
            script.onerror = function() {
                console.error('Failed to load script:', src);
                if (callback) callback(new Error('Script load failed'));
            };
            document.head.appendChild(script);
        }

        window.loadCryptoLibs = function() {
            return new Promise((resolve, reject) => {
                if (window.cryptoLibsLoaded) {
                    resolve();
                    return;
                }

                let loadedCount = 0;
                const totalLibs = 3;
                let hasError = false;

                function checkComplete(error) {
                    if (hasError) return;
                    
                    if (error) {
                        hasError = true;
                        reject(error);
                        return;
                    }
                    
                    loadedCount++;
                    if (loadedCount === totalLibs) {
                        window.cryptoLibsLoaded = true;
                        resolve();
                    }
                }

                loadScript('./lib/js/js-base64-main/base64.js', checkComplete);
                loadScript('https://cdnjs.cloudflare.com/ajax/libs/crypto-js/4.1.1/crypto-js.min.js', checkComplete);
                loadScript('./lib/js/sm-crypto/dist/sm4.js', checkComplete);
            });
        };
    </script>
</head>
<body>
    <h1>加密工具修复验证</h1>
    
    <div id="functionCheck" class="status info">
        正在检查函数定义...
    </div>
    
    <div id="loadTest" class="status info">
        等待测试库加载...
    </div>
    
    <button onclick="testLoadLibs()">测试加载库</button>
    <button onclick="testBase64()">测试Base64</button>
    <button onclick="resetTest()">重置测试</button>
    
    <div id="testResults"></div>

    <script>
        // 检查函数是否定义
        function checkFunctions() {
            const checkDiv = document.getElementById('functionCheck');
            
            if (typeof window.loadCryptoLibs === 'function') {
                checkDiv.className = 'status success';
                checkDiv.textContent = '✓ window.loadCryptoLibs 函数已正确定义';
            } else {
                checkDiv.className = 'status error';
                checkDiv.textContent = '✗ window.loadCryptoLibs 函数未定义';
            }
        }

        // 测试加载库
        async function testLoadLibs() {
            const loadDiv = document.getElementById('loadTest');
            const resultsDiv = document.getElementById('testResults');
            
            loadDiv.textContent = '正在加载库...';
            loadDiv.className = 'status info';
            
            try {
                await window.loadCryptoLibs();
                loadDiv.className = 'status success';
                loadDiv.textContent = '✓ 所有加密库加载成功';
                
                // 检查库是否可用
                let libStatus = '<h3>库状态检查:</h3>';
                libStatus += `<p>Base64: ${typeof Base64 !== 'undefined' ? '✓ 已加载' : '✗ 未加载'}</p>`;
                libStatus += `<p>CryptoJS: ${typeof CryptoJS !== 'undefined' ? '✓ 已加载' : '✗ 未加载'}</p>`;
                libStatus += `<p>sm4: ${typeof sm4 !== 'undefined' ? '✓ 已加载' : '✗ 未加载'}</p>`;
                
                resultsDiv.innerHTML = libStatus;
                
            } catch (error) {
                loadDiv.className = 'status error';
                loadDiv.textContent = '✗ 库加载失败: ' + error.message;
                resultsDiv.innerHTML = '<p class="error">请检查网络连接和文件路径</p>';
            }
        }

        // 测试Base64功能
        async function testBase64() {
            const resultsDiv = document.getElementById('testResults');
            
            try {
                await window.loadCryptoLibs();
                
                if (typeof Base64 === 'undefined') {
                    resultsDiv.innerHTML = '<p class="error">Base64库未加载</p>';
                    return;
                }
                
                const testText = 'Hello World 测试';
                const encoded = Base64.encode(testText);
                const decoded = Base64.decode(encoded);
                
                let result = '<h3>Base64 功能测试:</h3>';
                result += `<p><strong>原文:</strong> ${testText}</p>`;
                result += `<p><strong>加密:</strong> ${encoded}</p>`;
                result += `<p><strong>解密:</strong> ${decoded}</p>`;
                
                if (decoded === testText) {
                    result += '<p class="success">✓ Base64 加解密测试通过</p>';
                } else {
                    result += '<p class="error">✗ Base64 加解密测试失败</p>';
                }
                
                resultsDiv.innerHTML = result;
                
            } catch (error) {
                resultsDiv.innerHTML = `<p class="error">测试失败: ${error.message}</p>`;
            }
        }

        // 重置测试
        function resetTest() {
            window.cryptoLibsLoaded = false;
            document.getElementById('loadTest').className = 'status info';
            document.getElementById('loadTest').textContent = '等待测试库加载...';
            document.getElementById('testResults').innerHTML = '';
            
            // 移除已加载的脚本（仅用于测试）
            const scripts = document.querySelectorAll('script[src*="base64"], script[src*="crypto-js"], script[src*="sm4"]');
            scripts.forEach(script => script.remove());
            
            // 清除全局变量
            if (typeof Base64 !== 'undefined') delete window.Base64;
            if (typeof CryptoJS !== 'undefined') delete window.CryptoJS;
            if (typeof sm4 !== 'undefined') delete window.sm4;
        }

        // 页面加载完成后检查
        window.addEventListener('load', function() {
            checkFunctions();
        });
    </script>
</body>
</html>
