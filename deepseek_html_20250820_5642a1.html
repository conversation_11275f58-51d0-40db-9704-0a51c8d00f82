<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="约苗测试小工具 - 提供加密解密、数据处理等多种实用工具">
    <link rel="shortcut icon" href="https://img.scmttec.com/gw/laiyuemiao/<EMAIL>">
    <title>约苗小工具</title>
    
    <!-- 关键CSS优先加载 -->
    <link rel="stylesheet" href="./lib/css/tool.css">
    <link rel="stylesheet" href="https://img.scmttec.com/hospital/libs/element-ui2.15.6/theme-chalk/index.min.css">
    
    <!-- 预加载关键资源 -->
    <link rel="preload" href="./lib/js/vue.min.js" as="script">
    <link rel="preload" href="./lib/js/axios.min.js" as="script">
    <link rel="preload" href="https://img.scmttec.com/hospital/libs/element-ui.min.js" as="script">
    
    <!-- 核心JS库 -->
    <script src="./lib/js/vue.min.js"></script>
    <script src="./lib/js/axios.min.js"></script>
    <script src="https://img.scmttec.com/hospital/libs/element-ui.min.js"></script>
    
    <!-- 内联关键JS以减少请求 -->
    <script>
        // 工具函数和延迟加载逻辑
        window.utils = {
            // 延迟加载脚本
            loadScript: function(src, callback) {
                const script = document.createElement('script');
                script.src = src;
                script.onload = callback || function() {};
                script.onerror = function() {
                    console.error('Failed to load script:', src);
                    if (callback) callback(new Error('Script load failed'));
                };
                document.head.appendChild(script);
            },
            
            // 加载CSS
            loadCSS: function(href) {
                const link = document.createElement('link');
                link.rel = 'stylesheet';
                link.href = href;
                document.head.appendChild(link);
            },
            
            // 复制文本到剪贴板
            copyToClipboard: function(text) {
                return new Promise((resolve, reject) => {
                    const textarea = document.createElement('textarea');
                    textarea.value = text;
                    textarea.style.position = 'fixed';
                    textarea.style.opacity = '0';
                    document.body.appendChild(textarea);
                    textarea.select();
                    
                    try {
                        const successful = document.execCommand('copy');
                        document.body.removeChild(textarea);
                        successful ? resolve() : reject(new Error('Copy failed'));
                    } catch (err) {
                        document.body.removeChild(textarea);
                        reject(err);
                    }
                });
            },
            
            // 显示消息
            showMessage: function(message, type = 'info') {
                if (window.app && window.app.$message) {
                    window.app.$message({
                        showClose: true,
                        message: message,
                        type: type
                    });
                } else {
                    console.log(`${type}: ${message}`);
                }
            }
        };
        
        // 按需加载加密库
        window.loadCryptoLibs = function() {
            return new Promise((resolve, reject) => {
                if (window.cryptoLibsLoaded) {
                    resolve();
                    return;
                }
                
                let loadedCount = 0;
                const totalLibs = 3;
                let hasError = false;
                
                function checkComplete(error) {
                    if (hasError) return;
                    
                    if (error) {
                        hasError = true;
                        reject(error);
                        return;
                    }
                    
                    loadedCount++;
                    if (loadedCount === totalLibs) {
                        window.cryptoLibsLoaded = true;
                        resolve();
                    }
                }
                
                // 加载 Base64 库
                utils.loadScript('./lib/js/js-base64-main/base64.js', checkComplete);
                
                // 加载 CryptoJS 库
                utils.loadScript('https://cdnjs.cloudflare.com/ajax/libs/crypto-js/4.1.1/crypto-js.min.js', checkComplete);
                
                // 加载 SM4 库
                utils.loadScript('./lib/js/sm-crypto/dist/sm4.js', checkComplete);
            });
        };
        
        // 按需加载CodeMirror
        window.loadCodeMirror = function() {
            return new Promise((resolve, reject) => {
                if (window.codeMirrorLoaded) {
                    resolve();
                    return;
                }
                
                utils.loadCSS('https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.62.0/codemirror.min.css');
                utils.loadScript('https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.62.0/codemirror.min.js', function(error) {
                    if (error) {
                        reject(error);
                        return;
                    }
                    utils.loadScript('https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.62.0/mode/yaml/yaml.min.js', function(error) {
                        if (error) {
                            reject(error);
                        } else {
                            window.codeMirrorLoaded = true;
                            resolve();
                        }
                    });
                });
            });
        };
    </script>
    
    <style>
        /* 关键CSS内联以提高首屏加载速度 */
        [v-cloak] { display: none; }
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.9);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }
        .img-data {
            max-width: 100%;
            height: auto;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .my-iframe {
            width: 100%;
            height: 600px;
            border: none;
            border-radius: 4px;
        }
        .link-wrapper {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        .copy-btn {
            margin-left: 10px;
        }
        .tool-card-content {
            text-align: center;
            padding: 15px;
        }
        .tool-icon {
            width: 40px;
            height: 40px;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div id="app" v-cloak>
        <!-- 全局加载指示器 -->
        <div class="loading-overlay" v-if="globalLoading">
            <el-button type="primary" :loading="true">加载中</el-button>
        </div>
        
        <el-container class="operation-wrapper">
            <el-header>
                <div class="header-content">
                    <div class="header-left">
                        <img class="header-img" :src="logo" alt="约苗logo" loading="lazy">
                        <h2>测试小工具</h2>
                    </div>
                    <el-menu
                        :default-active="activeMenu"
                        class="header-menu"
                        mode="horizontal"
                        @select="handleMenuSelect"
                        background-color="transparent"
                        text-color="#fff"
                        active-text-color="#fff">
                        <!-- 核心功能组 -->
                        <el-menu-item index="workbench">
                            <i class="el-icon-s-platform"></i>工作台
                        </el-menu-item>

                        <!-- 加密解密工具组 -->
                        <el-submenu index="crypto">
                            <template slot="title">
                                <i class="el-icon-lock"></i>加密解密工具
                            </template>
                            <el-menu-item index="decrypt">
                                <i class="el-icon-key"></i>埋点解密
                            </el-menu-item>
                            <el-menu-item index="base64">
                                <i class="el-icon-key"></i>Base64加解密
                            </el-menu-item>
                            <el-menu-item index="sm4">
                                <i class="el-icon-lock"></i>SM4加解密
                            </el-menu-item>
                            <el-menu-item index="aes">
                                <i class="el-icon-key"></i>AES加密解析
                            </el-menu-item>
                        </el-submenu>

                        <!-- 数据处理工具组 -->
                        <el-submenu index="data">
                            <template slot="title">
                                <i class="el-icon-data-analysis"></i>数据处理工具
                            </template>
                            <el-menu-item index="doris">
                                <i class="el-icon-data-analysis"></i>Doris
                            </el-menu-item>
                            <el-menu-item index="json">
                                <i class="el-icon-document"></i>Json格式化
                            </el-menu-item>
                            <el-menu-item index="url">
                                <i class="el-icon-link"></i>URL编码解码
                            </el-menu-item>
                            <el-menu-item index="timestamp">
                                <i class="el-icon-time"></i>时间戳转换
                            </el-menu-item>
                        </el-submenu>

                        <!-- 实用工具组 -->
                        <el-submenu index="utils">
                            <template slot="title">
                                <i class="el-icon-s-tools"></i>实用工具
                            </template>
                            <el-menu-item index="image">
                                <i class="el-icon-picture"></i>图片生成
                            </el-menu-item>
                            <el-menu-item index="idcard">
                                <i class="el-icon-document"></i>身份证生成
                            </el-menu-item>
                            <el-menu-item index="location">
                                <i class="el-icon-location"></i>经纬度查询
                            </el-menu-item>
                            <el-menu-item index="cron">
                                <i class="el-icon-timer"></i>Cron表达式
                            </el-menu-item>
                        </el-submenu>

                        <!-- 更新日志 -->
                        <el-menu-item index="logs">
                            <i class="el-icon-notebook-2"></i>更新日志
                        </el-menu-item>
                    </el-menu>
                </div>
            </el-header>
            
            <el-main>
                <div class="main-container">
                    <!-- 工作台 -->
                    <div v-show="activeMenu === 'workbench'" class="section-container">
                        <!-- 工作台内容保持不变，但添加加载状态 -->
                        <div class="section-header">
                            <h2><i class="el-icon-s-platform"></i> 工作台</h2>
                        </div>
                        <div class="section-content">
                            <!-- 内容保持不变 -->
                        </div>
                    </div>
                    
                    <!-- 其他工具部分保持不变，但添加适当的加载状态 -->
                    <!-- 图片生成 -->
                    <div v-show="activeMenu === 'image'" class="section-container">
                        <!-- 内容保持不变 -->
                    </div>
                    
                    <!-- 约苗埋点数据解密 -->
                    <div v-show="activeMenu === 'decrypt'" class="section-container">
                        <!-- 内容保持不变 -->
                    </div>
                    
                    <!-- 其他部分保持不变 -->
                    
                    <!-- 版本更新日志 -->
                    <div v-show="activeMenu === 'logs'" class="section-container">
                        <!-- 内容保持不变 -->
                    </div>

                    <el-backtop target=".operation-wrapper"><i class="el-icon-caret-top"></i></el-backtop>
                </div>
            </el-main>
        </el-container>
    </div>
    
    <script>
        // 将Vue应用赋值给全局变量以便工具函数访问
        window.app = new Vue({
            el: "#app",
            data: {
                globalLoading: false,
                tabPosition: "left",
                width: "",
                height: "",
                quantity: "",
                btShow: false,
                imgList: [],
                btStatus: false,
                activeName: '1',
                activeMenu: 'workbench',
                jsonStr: "",
                logo: "https://img.scmttec.com/gw/laiyuemiao/<EMAIL>",
                dorisLink: "https://doris.incubator.apache.org/zh-CN/docs/gettingStarted/what-is-new",
                funcName: "zero",
                yamlContent: '',
                errorOutput: '',
                inputText: '',
                sm4inputText1: '',
                sm4inputText: '',
                outputText: '',
                inputJson: '',
                outputJson: '',
                tableData: [],
                apkPath: [],
                logData: [],
                aiData: [],
                securityData: [],
                testPlatfromData: [],
                commandList: [],
                timeClass: [],
                aggregationClass: [],
                bitmapClass: [],
                sqlClass: [],
                otherClass: [],
                show: true,
                copyButtonIndex: -1,
                copyButtonType: '',
                visible: false,
                testUrl: 'http://192.168.20.246:3301',
                preUrl: 'http://192.168.20.246:3302',
                encryptTool: "base64",
                aesKey: "yuemiao_zW4NcAbR",
                
                // 添加加载状态
                loadingStates: {
                    doris: false,
                    logs: false,
                    crypto: false
                }
            },
            
            // 优化数据加载 - 懒加载策略
            async created() {
                // 只加载工作台必需的数据
                try {
                    this.globalLoading = true;
                    await Promise.all([
                        this.getPlatformUrl(),
                        this.getApkUrl(),
                        this.getAiUrl(),
                        this.getSecurityUrl(),
                        this.getTestPlatfromUrl()
                    ]);
                } catch (error) {
                    console.error('核心数据加载失败:', error);
                    this.showMessage('数据加载失败，请刷新页面重试', 'error');
                } finally {
                    this.globalLoading = false;
                }
            },
            
            mounted() {
                // 添加页面加载完成事件
                setTimeout(() => {
                    window.dispatchEvent(new Event('app-mounted'));
                }, 500);
            },
            
            methods: {
                // 统一消息显示方法
                showMessage(message, type = 'info') {
                    this.$message({
                        showClose: true,
                        message: message,
                        type: type
                    });
                },
                
                // 图片生成方法
                async createImg() {
                    if(!this.width || !this.height || !this.quantity) {
                        this.showMessage('图片宽度、高度和数量不能为空！', 'warning');
                        return;
                    }
                    
                    this.imgList = [];
                    this.btStatus = true;
                    
                    try {
                        const requests = [];
                        for(let i = 0; i < this.quantity; i++) {
                            requests.push(
                                axios.get(`https://picsum.photos/${this.width}/${this.height}`)
                                    .then(response => {
                                        this.imgList.push(response.request.responseURL);
                                        this.showMessage(`已生成：${this.imgList.length}/${this.quantity}`, 'success');
                                    })
                            );
                            
                            // 添加延迟避免请求过于频繁
                            if(i > 0 && i % 5 === 0) {
                                await new Promise(resolve => setTimeout(resolve, 500));
                            }
                        }
                        
                        await Promise.all(requests);
                    } catch (error) {
                        console.error('图片生成失败:', error);
                        this.showMessage('部分图片生成失败', 'error');
                    } finally {
                        this.btStatus = false;
                    }
                },
                
                // 数据获取方法
                async getPlatformUrl() {
                    try {
                        const response = await axios.get("./data/platformUrl.json");
                        this.tableData = response.data.data.platformUrl;
                    } catch (error) {
                        console.error('获取平台URL失败:', error);
                        throw error;
                    }
                },
                
                async getApkUrl() {
                    try {
                        const response = await axios.get("./data/apkUrl.json");
                        this.apkPath = response.data.data.apkUrl;
                    } catch (error) {
                        console.error('获取APK URL失败:', error);
                        throw error;
                    }
                },
                
                async getLogs() {
                    try {
                        this.loadingStates.logs = true;
                        const response = await axios.get("./data/log.json");
                        this.logData = response.data.data.logs;
                    } catch (error) {
                        console.error('获取日志失败:', error);
                        throw error;
                    } finally {
                        this.loadingStates.logs = false;
                    }
                },
                
                async getAiUrl() {
                    try {
                        const response = await axios.get("./data/ai.json");
                        this.aiData = response.data.data.aiUrl;
                    } catch (error) {
                        console.error('获取AI URL失败:', error);
                        throw error;
                    }
                },
                
                async getSecurityUrl() {
                    try {
                        const response = await axios.get("./data/security.json");
                        this.securityData = response.data.data.securityUrl;
                    } catch (error) {
                        console.error('获取安全URL失败:', error);
                        throw error;
                    }
                },
                
                async getTestPlatfromUrl() {
                    try {
                        const response = await axios.get("./data/testPlatform.json");
                        this.testPlatfromData = response.data.data.testPlatfromUrl;
                    } catch (error) {
                        console.error('获取测试平台URL失败:', error);
                        throw error;
                    }
                },
                
                async getFunctionList() {
                    try {
                        this.loadingStates.doris = true;
                        const response = await axios.get("./data/function.json");
                        const dataAll = response.data.data;
                        this.commandList = dataAll.commandList;
                        this.timeClass = dataAll.timeClass;
                        this.aggregationClass = dataAll.aggregationClass;
                        this.bitmapClass = dataAll.bitmapClass;
                        this.sqlClass = dataAll.sqlClass;
                        this.otherClass = dataAll.otherClass;
                    } catch (error) {
                        console.error('获取函数列表失败:', error);
                        throw error;
                    } finally {
                        this.loadingStates.doris = false;
                    }
                },
                
                // 其他方法保持不变，但添加错误处理
                clearData() {
                    this.width = "";
                    this.height = "";
                    this.imgList = [];
                    this.quantity = "";
                    this.btShow = false;
                    this.showMessage('数据重置成功！');
                },
                
                // 其他方法...
                
                // 优化后的菜单选择处理
                async handleMenuSelect(key) {
                    this.activeMenu = key;

                    // 按需加载数据和库
                    try {
                        if (key === 'doris' && this.commandList.length === 0) {
                            await this.getFunctionList();
                        }

                        if (key === 'logs' && this.logData.length === 0) {
                            await this.getLogs();
                        }

                        // 按需加载加密库
                        if (['decrypt', 'base64', 'sm4', 'aes'].includes(key)) {
                            this.loadingStates.crypto = true;
                            await window.loadCryptoLibs();
                            console.log('加密库加载完成');
                        }
                    } catch (error) {
                        console.error(`${key} 数据加载失败:`, error);
                        this.showMessage(`${key} 数据加载失败，请刷新页面重试`, 'error');
                    } finally {
                        this.loadingStates.crypto = false;
                    }
                }
            }
        });
    </script>
</body>
</html>