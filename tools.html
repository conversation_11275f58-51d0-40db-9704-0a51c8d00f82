<script type="text/javascript">
        var gk_isXlsx = false;
        var gk_xlsxFileLookup = {};
        var gk_fileData = {};
        function filledCell(cell) {
          return cell !== '' && cell != null;
        }
        function loadFileData(filename) {
        if (gk_isXlsx && gk_xlsxFileLookup[filename]) {
            try {
                var workbook = XLSX.read(gk_fileData[filename], { type: 'base64' });
                var firstSheetName = workbook.SheetNames[0];
                var worksheet = workbook.Sheets[firstSheetName];

                // Convert sheet to JSON to filter blank rows
                var jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1, blankrows: false, defval: '' });
                // Filter out blank rows (rows where all cells are empty, null, or undefined)
                var filteredData = jsonData.filter(row => row.some(filledCell));

                // Heuristic to find the header row by ignoring rows with fewer filled cells than the next row
                var headerRowIndex = filteredData.findIndex((row, index) =>
                  row.filter(filledCell).length >= filteredData[index + 1]?.filter(filledCell).length
                );
                // Fallback
                if (headerRowIndex === -1 || headerRowIndex > 25) {
                  headerRowIndex = 0;
                }

                // Convert filtered JSON back to CSV
                var csv = XLSX.utils.aoa_to_sheet(filteredData.slice(headerRowIndex)); // Create a new sheet from filtered array of arrays
                csv = XLSX.utils.sheet_to_csv(csv, { header: 1 });
                return csv;
            } catch (e) {
                console.error(e);
                return "";
            }
        }
        return gk_fileData[filename] || "";
        }
        </script><!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试小工具</title>
    <!-- Include Tailwind CSS for styling -->
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 font-sans">
    <!-- Header Section -->
    <header class="bg-blue-600 text-white py-4">
        <div class="container mx-auto px-4">
            <h1 class="text-2xl font-bold">测试小工具</h1>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto px-4 py-8">
        <!-- Navigation Menu -->
        <nav class="mb-8">
            <ul class="flex flex-wrap gap-4">
                <li><a href="#workbench" class="text-blue-600 hover:underline">工作台</a></li>
                <li><a href="#update-log" class="text-blue-600 hover:underline">更新日志</a></li>
            </ul>
        </nav>

        <!-- Workbench Section -->
        <section id="workbench" class="mb-12">
            <h2 class="text-xl font-semibold mb-4">工作台</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <!-- Tool Categories -->
                <div class="bg-white p-6 rounded-lg shadow">
                    <h3 class="text-lg font-medium mb-2">加密解密工具</h3>
                    <ul class="list-disc pl-5">
                        <li><a href="#decrypt" class="text-blue-600 hover:underline">埋点解密</a></li>
                        <li><a href="#base64" class="text-blue-600 hover:underline">Base64加解密</a></li>
                        <li><a href="#sm4" class="text-blue-600 hover:underline">SM4加解密</a></li>
                        <li><a href="#aes" class="text-blue-600 hover:underline">AES加密解析</a></li>
                    </ul>
                </div>
                <div class="bg-white p-6 rounded-lg shadow">
                    <h3 class="text-lg font-medium mb-2">数据处理工具</h3>
                    <ul class="list-disc pl-5">
                        <li><a href="#doris" class="text-blue-600 hover:underline">Doris</a></li>
                        <li><a href="#json" class="text-blue-600 hover:underline">Json格式化</a></li>
                        <li><a href="#url" class="text-blue-600 hover:underline">URL编码解码</a></li>
                        <li><a href="#timestamp" class="text-blue-600 hover:underline">时间戳转换</a></li>
                    </ul>
                </div>
                <div class="bg-white p-6 rounded-lg shadow">
                    <h3 class="text-lg font-medium mb-2">实用工具</h3>
                    <ul class="list-disc pl-5">
                        <li><a href="#image-gen" class="text-blue-600 hover:underline">图片生成</a></li>
                        <li><a href="#id-gen" class="text-blue-600 hover:underline">身份证生成</a></li>
                        <li><a href="#geo" class="text-blue-600 hover:underline">经纬度查询</a></li>
                        <li><a href="#cron" class="text-blue-600 hover:underline">Cron表达式</a></li>
                    </ul>
                </div>
            </div>

            <!-- AI Tools -->
            <div class="bg-white p-6 rounded-lg shadow mt-6">
                <h3 class="text-lg font-medium mb-2">AI工具</h3>
                <ul class="list-disc pl-5">
                    <li v-for="item in aiTools" :key="item.platformName">
                        {{item.platformName}} - {{item.remark}}
                    </li>
                </ul>
            </div>

            <!-- Security Testing -->
            <div class="bg-white p-6 rounded-lg shadow mt-6">
                <h3 class="text-lg font-medium mb-2">安全测试-练习靶场</h3>
                <ul class="list-disc pl-5">
                    <li v-for="item in securityTools" :key="item.platformName">
                        {{item.platformName}} - {{item.account}}
                    </li>
                </ul>
            </div>

            <!-- Testing Platforms -->
            <div class="bg-white p-6 rounded-lg shadow mt-6">
                <h3 class="text-lg font-medium mb-2">测试平台</h3>
                <ul class="list-disc pl-5">
                    <li v-for="item in testPlatforms" :key="item.platformName">
                        {{item.platformName}} - {{item.account}}
                    </li>
                </ul>
            </div>

            <!-- Platform Links -->
            <div class="bg-white p-6 rounded-lg shadow mt-6">
                <h3 class="text-lg font-medium mb-2">平台链接</h3>
                <div v-for="scope in platformLinks" :key="scope.row.test_url" class="mb-2">
                    <p>
                        <a :href="scope.row.test_url" class="text-blue-600 hover:underline">{{scope.row.test_url}}</a>
                        <button class="ml-2 text-blue-600 hover:underline">复制链接</button>
                    </p>
                    <p>
                        <a :href="scope.row.pre_url" class="text-blue-600 hover:underline">{{scope.row.pre_url}}</a>
                        <button class="ml-2 text-blue-600 hover:underline">复制链接</button>
                    </p>
                    <p>
                        <a :href="scope.row.prod_url" class="text-blue-600 hover:underline">{{scope.row.prod_url}}</a>
                        <button class="ml-2 text-blue-600 hover:underline">复制链接</button>
                    </p>
                </div>
            </div>

            <!-- APP Installation Packages -->
            <div class="bg-white p-6 rounded-lg shadow mt-6">
                <h3 class="text-lg font-medium mb-2">APP安装包</h3>
                <div v-for="scope in appPackages" :key="scope.row.test_url" class="mb-2">
                    <p>
                        <a :href="scope.row.test_url" class="text-blue-600 hover:underline">{{scope.row.test_url}}</a>
                        <button class="ml-2 text-blue-600 hover:underline">复制链接</button>
                    </p>
                    <p>
                        <a :href="scope.row.pre_url" class="text-blue-600 hover:underline">{{scope.row.pre_url}}</a>
                        <button class="ml-2 text-blue-600 hover:underline">复制链接</button>
                    </p>
                    <p>
                        <a :href="scope.row.prod_url" class="text-blue-600 hover:underline">{{scope.row.prod_url}}</a>
                        <button class="ml-2 text-blue-600 hover:underline">复制链接</button>
                    </p>
                </div>
            </div>
        </section>

        <!-- Image Generation Section -->
        <section id="image-gen" class="mb-12">
            <h2 class="text-xl font-semibold mb-4">图片生成</h2>
            <div class="bg-white p-6 rounded-lg shadow">
                <form class="space-y-4">
                    <div>
                        <label for="img-width" class="block">图片宽度：</label>
                        <input id="img-width" type="number" class="border p-2 rounded w-full">
                    </div>
                    <div>
                        <label for="img-height" class="block">图片高度：</label>
                        <input id="img-height" type="number" class="border p-2 rounded w-full">
                    </div>
                    <div>
                        <label for="img-count" class="block">图片数量：</label>
                        <input id="img-count" type="number" class="border p-2 rounded w-full">
                    </div>
                    <div class="flex gap-4">
                        <button type="button" class="bg-blue-600 text-white px-4 py-2 rounded">生成图片</button>
                        <button type="button" class="bg-gray-600 text-white px-4 py-2 rounded">数据重置</button>
                    </div>
                </form>
                <div class="mt-6">
                    <h3 class="text-lg font-medium">图片展示区</h3>
                    <p class="text-sm text-gray-600">温馨提示：由于图片是从远程服务端获取，可能出现加载延迟，请耐心等待哦！</p>
                    <div class="grid grid-cols-2 gap-4 mt-4">
                        <img src="" alt="Generated Image" class="w-full h-auto">
                        <img src="" alt="Generated Image" class="w-full h-auto">
                    </div>
                </div>
            </div>
        </section>

        <!-- Decryption Section -->
        <section id="decrypt" class="mb-12">
            <h2 class="text-xl font-semibold mb-4">约苗埋点数据解密</h2>
            <div class="bg-white p-6 rounded-lg shadow">
                <h3 class="text-lg font-medium mb-2">工具说明</h3>
                <p>ANDROID APP请使用AES加解密工具</p>
                <p>IOS APP、微信公号请使用Base64加解密工具</p>

                <!-- Base64 Section -->
                <div class="mt-4">
                    <h4 class="text-md font-medium">Base64</h4>
                    <div class="flex gap-4 mb-4">
                        <button class="bg-blue-600 text-white px-4 py-2 rounded">Base64加密</button>
                        <button class="bg-blue-600 text-white px-4 py-2 rounded">解密并复制</button>
                        <button class="bg-gray-600 text-white px-4 py-2 rounded">数据重置</button>
                    </div>
                    <div class="space-y-4">
                        <div>
                            <label for="base64-encrypt" class="block">原始加密数据</label>
                            <textarea id="base64-encrypt" class="border p-2 rounded w-full"></textarea>
                        </div>
                        <div>
                            <label for="base64-decrypt" class="block">原始解密数据</label>
                            <textarea id="base64-decrypt" class="border p-2 rounded w-full"></textarea>
                        </div>
                        <div>
                            <label for="base64-formatted" class="block">格式化解密数据</label>
                            <textarea id="base64-formatted" class="border p-2 rounded w-full"></textarea>
                        </div>
                    </div>
                </div>

                <!-- AES Section -->
                <div class="mt-4">
                    <h4 class="text-md font-medium">AES</h4>
                    <div class="flex gap-4 mb-4">
                        <button class="bg-blue-600 text-white px-4 py-2 rounded">AES加密</button>
                        <button class="bg-blue-600 text-white px-4 py-2 rounded">解密并复制</button>
                        <button class="bg-gray-600 text-white px-4 py-2 rounded">数据重置</button>
                    </div>
                    <div class="space-y-4">
                        <div>
                            <label for="aes-encrypt" class="block">原始加密数据</label>
                            <textarea id="aes-encrypt" class="border p-2 rounded w-full"></textarea>
                        </div>
                        <div>
                            <label for="aes-decrypt" class="block">原始解密数据</label>
                            <textarea id="aes-decrypt" class="border p-2 rounded w-full"></textarea>
                        </div>
                        <div>
                            <label for="aes-formatted" class="block">格式化解密数据</label>
                            <textarea id="aes-formatted" class="border p-2 rounded w-full"></textarea>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Doris Section -->
        <section id="doris" class="mb-12">
            <h2 class="text-xl font-semibold mb-4">Doris</h2>
            <div class="bg-white p-6 rounded-lg shadow">
                <h3 class="text-lg font-medium mb-2">注意事项</h3>
                <ul class="list-disc pl-5">
                    <li>相关数据表：tb_tag(标签) tb_base_tag(标签查询规则) tb_tag_users(用户标签明细) tb_user_group(用户分群) tb_user_group_user(用户分群明细) tb_user_group_task(用户分群任务)</li>
                    <li>新建分群在整十分会自动更新用户分群的数据、历史分群每天凌晨1-2点全量更新</li>
                    <li>查看日志路径：tail -f /data/task-{env}/logs/scheduler.log</li>
                    <li>若当天标签数据需要重新生成，请手动在tb_tag_users表中删除对应id的标签，tb_tag表中对应的标签的update_time清空，在执行用户标签脚本</li>
                    <li>若当天分群数据需要重新生成，清除分群的tb_user_group_task对应任务数据，然后把分群任务设置为手动更新，再执行用户分群脚本</li>
                </ul>
                <div class="mt-4">
                    <h3 class="text-lg font-medium mb-2">任务列表</h3>
                    <div class="space-y-4">
                        <div>
                            <h4 class="text-md font-medium">测试环境-数据同步</h4>
                            <ul class="list-disc pl-5">
                                <li>用户标签</li>
                                <li>用户分群</li>
                                <li>数据概览</li>
                                <li>事件统计</li>
                            </ul>
                        </div>
                        <div>
                            <h4 class="text-md font-medium">预发环境-数据同步</h4>
                            <ul class="list-disc pl-5">
                                <li>用户标签</li>
                                <li>用户分群</li>
                                <li>数据概览</li>
                                <li>事件统计</li>
                            </ul>
                        </div>
                    </div>
                    <div class="mt-4">
                        <h4 class="text-md font-medium">Doris常用命令和函数</h4>
                        <a href="#" class="text-blue-600 hover:underline">Doris操作手册</a>
                    </div>
                </div>
            </div>
        </section>

        <!-- Other Tool Sections (Base64, SM4, JSON, etc.) -->
        <section id="base64" class="mb-12">
            <h2 class="text-xl font-semibold mb-4">Base64加解密</h2>
            <div class="bg-white p-6 rounded-lg shadow">
                <!-- Add Base64 tool content here -->
            </div>
        </section>

        <section id="sm4" class="mb-12">
            <h2 class="text-xl font-semibold mb-4">SM4加解密</h2>
            <div class="bg-white p-6 rounded-lg shadow">
                <div class="space-y-4">
                    <div>
                        <label for="sm4-text" class="block">待加密/解密的文本</label>
                        <textarea id="sm4-text" class="border p-2 rounded w-full"></textarea>
                    </div>
                    <div class="flex gap-4">
                        <button class="bg-blue-600 text-white px-4 py-2 rounded">SM4加密</button>
                        <button class="bg-blue-600 text-white px-4 py-2 rounded">SM4解密</button>
                        <button class="bg-blue-600 text-white px-4 py-2 rounded">复制结果</button>
                    </div>
                    <div>
                        <label for="sm4-result" class="block">加密/解密后数据</label>
                        <textarea id="sm4-result" class="border p-2 rounded w-full"></textarea>
                    </div>
                    <p>
                        <a href="#" class="text-blue-600 hover:underline">对解密结果不满意？点击跳转外部解密网站</a>
                    </p>
                </div>
            </div>
        </section>

        <section id="json" class="mb-12">
            <h2 class="text-xl font-semibold mb-4">Json格式化</h2>
            <div class="bg-white p-6 rounded-lg shadow">
                <!-- Add JSON formatter content here -->
            </div>
        </section>

        <section id="aes" class="mb-12">
            <h2 class="text-xl font-semibold mb-4">AES加密解析</h2>
            <div class="bg-white p-6 rounded-lg shadow">
                <p>AES加密密码：yuemiao_zW4NcAbR</p>
                <p>填充：pkcs5padding</p>
                <p>字符集：utf8编码（unicode编码）</p>
                <!-- Add AES tool content here -->
            </div>
        </section>

        <section id="url" class="mb-12">
            <h2 class="text-xl font-semibold mb-4">URL编码解码</h2>
            <div class="bg-white p-6 rounded-lg shadow">
                <!-- Add URL encoder/decoder content here -->
            </div>
        </section>

        <section id="timestamp" class="mb-12">
            <h2 class="text-xl font-semibold mb-4">时间戳转换</h2>
            <div class="bg-white p-6 rounded-lg shadow">
                <!-- Add timestamp converter content here -->
            </div>
        </section>

        <section id="geo" class="mb-12">
            <h2 class="text-xl font-semibold mb-4">经纬度查询</h2>
            <div class="bg-white p-6 rounded-lg shadow">
                <!-- Add geolocation query content here -->
            </div>
        </section>

        <section id="cron" class="mb-12">
            <h2 class="text-xl font-semibold mb-4">Cron表达式</h2>
            <div class="bg-white p-6 rounded-lg shadow">
                <!-- Add cron expression content here -->
            </div>
        </section>

        <!-- Update Log Section -->
        <section id="update-log" class="mb-12">
            <h2 class="text-xl font-semibold mb-4">版本更新日志</h2>
            <div class="bg-white p-6 rounded-lg shadow">
                <ul class="space-y-4">
                    <li v-for="item in updateLogs" :key="item.title">
                        <h3 class="text-lg font-medium">{{item.title}}</h3>
                        <p>{{item.commit_time}}</p>
                        <p>{{item.commit_content}}</p>
                    </li>
                </ul>
            </div>
        </section>
    </main>

    <!-- Footer Section -->
    <footer class="bg-gray-800 text-white py-4">
        <div class="container mx-auto px-4">
            <p>&copy; 2025 测试小工具. All rights reserved.</p>
        </div>
    </footer>
</body>
</html>