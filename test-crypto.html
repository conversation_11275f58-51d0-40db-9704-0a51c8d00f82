<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>加密工具测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 4px;
            word-break: break-all;
        }
        .error {
            color: red;
        }
        .success {
            color: green;
        }
        input, textarea {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        textarea {
            height: 100px;
            resize: vertical;
        }
    </style>

    <!-- 工具库加载函数 - 需要在页面内容之前定义 -->
    <script>
        // 延迟加载非关键JS库
        function loadScript(src, callback) {
            const script = document.createElement('script');
            script.src = src;
            script.onload = callback || function() {};
            script.onerror = function() {
                console.error('Failed to load script:', src);
                if (callback) callback(new Error('Script load failed'));
            };
            document.head.appendChild(script);
        }

        // 加载加密库
        window.loadCryptoLibs = function() {
            return new Promise((resolve, reject) => {
                if (window.cryptoLibsLoaded) {
                    resolve();
                    return;
                }

                let loadedCount = 0;
                const totalLibs = 3;
                let hasError = false;

                function checkComplete(error) {
                    if (hasError) return;

                    if (error) {
                        hasError = true;
                        reject(error);
                        return;
                    }

                    loadedCount++;
                    if (loadedCount === totalLibs) {
                        window.cryptoLibsLoaded = true;
                        resolve();
                    }
                }

                // 加载 Base64 库
                loadScript('./lib/js/js-base64-main/base64.js', checkComplete);

                // 加载 CryptoJS 库
                loadScript('https://cdnjs.cloudflare.com/ajax/libs/crypto-js/4.1.1/crypto-js.min.js', checkComplete);

                // 加载 SM4 库
                loadScript('./lib/js/sm-crypto/dist/sm4.js', checkComplete);
            });
        };
    </script>
</head>
<body>
    <h1>加密工具测试页面</h1>
    
    <div class="test-section">
        <h3>Base64 加解密测试</h3>
        <input type="text" id="base64Input" placeholder="输入要加密/解密的文本" value="Hello World">
        <br>
        <button onclick="testBase64Encode()">Base64 加密</button>
        <button onclick="testBase64Decode()">Base64 解密</button>
        <div id="base64Result" class="result"></div>
    </div>

    <div class="test-section">
        <h3>SM4 加解密测试</h3>
        <input type="text" id="sm4Input" placeholder="输入要加密/解密的文本" value="Hello World">
        <br>
        <button onclick="testSM4Encrypt()">SM4 加密</button>
        <button onclick="testSM4Decrypt()">SM4 解密</button>
        <div id="sm4Result" class="result"></div>
    </div>

    <div class="test-section">
        <h3>AES 加解密测试</h3>
        <input type="text" id="aesInput" placeholder="输入要加密/解密的文本" value="Hello World">
        <br>
        <button onclick="testAESEncrypt()">AES 加密</button>
        <button onclick="testAESDecrypt()">AES 解密</button>
        <div id="aesResult" class="result"></div>
    </div>

    <div class="test-section">
        <h3>库加载状态</h3>
        <button onclick="checkLibraries()">检查库状态</button>
        <button onclick="loadLibraries()">加载加密库</button>
        <div id="libStatus" class="result"></div>
    </div>

    <script>

        // 检查库状态
        function checkLibraries() {
            const status = document.getElementById('libStatus');
            let html = '<h4>库加载状态:</h4>';
            
            html += `<p>Base64: ${typeof Base64 !== 'undefined' ? '<span class="success">已加载</span>' : '<span class="error">未加载</span>'}</p>`;
            html += `<p>CryptoJS: ${typeof CryptoJS !== 'undefined' ? '<span class="success">已加载</span>' : '<span class="error">未加载</span>'}</p>`;
            html += `<p>SM4: ${typeof sm4 !== 'undefined' ? '<span class="success">已加载</span>' : '<span class="error">未加载</span>'}</p>`;
            html += `<p>cryptoLibsLoaded: ${window.cryptoLibsLoaded ? '<span class="success">true</span>' : '<span class="error">false</span>'}</p>`;
            
            status.innerHTML = html;
        }

        // 加载库
        async function loadLibraries() {
            const status = document.getElementById('libStatus');
            status.innerHTML = '<p>正在加载库...</p>';
            
            try {
                await window.loadCryptoLibs();
                status.innerHTML = '<p class="success">所有库加载成功!</p>';
                checkLibraries();
            } catch (error) {
                status.innerHTML = `<p class="error">库加载失败: ${error.message}</p>`;
            }
        }

        // Base64 测试
        async function testBase64Encode() {
            const input = document.getElementById('base64Input').value;
            const result = document.getElementById('base64Result');
            
            try {
                await window.loadCryptoLibs();
                if (typeof Base64 === 'undefined') {
                    result.innerHTML = '<p class="error">Base64库未加载</p>';
                    return;
                }
                
                const encoded = Base64.encode(input);
                result.innerHTML = `<p class="success">加密结果: ${encoded}</p>`;
            } catch (error) {
                result.innerHTML = `<p class="error">加密失败: ${error.message}</p>`;
            }
        }

        async function testBase64Decode() {
            const input = document.getElementById('base64Input').value;
            const result = document.getElementById('base64Result');
            
            try {
                await window.loadCryptoLibs();
                if (typeof Base64 === 'undefined') {
                    result.innerHTML = '<p class="error">Base64库未加载</p>';
                    return;
                }
                
                const decoded = Base64.decode(input);
                result.innerHTML = `<p class="success">解密结果: ${decoded}</p>`;
            } catch (error) {
                result.innerHTML = `<p class="error">解密失败: ${error.message}</p>`;
            }
        }

        // SM4 测试
        async function testSM4Encrypt() {
            const input = document.getElementById('sm4Input').value;
            const result = document.getElementById('sm4Result');
            
            try {
                await window.loadCryptoLibs();
                if (typeof sm4 === 'undefined') {
                    result.innerHTML = '<p class="error">SM4库未加载</p>';
                    return;
                }
                
                const key = "1234567812345678";
                const hexKey = strToHex(key);
                const encrypted = sm4.encrypt(input, hexKey);
                const base64Result = window.btoa(hexStringToString(encrypted));
                result.innerHTML = `<p class="success">加密结果: ${base64Result}</p>`;
            } catch (error) {
                result.innerHTML = `<p class="error">加密失败: ${error.message}</p>`;
            }
        }

        async function testSM4Decrypt() {
            const input = document.getElementById('sm4Input').value;
            const result = document.getElementById('sm4Result');
            
            try {
                await window.loadCryptoLibs();
                if (typeof sm4 === 'undefined') {
                    result.innerHTML = '<p class="error">SM4库未加载</p>';
                    return;
                }
                
                const key = "1234567812345678";
                const hexKey = strToHex(key);
                const hexInput = base64ToHex(input);
                const decrypted = sm4.decrypt(hexInput, hexKey);
                result.innerHTML = `<p class="success">解密结果: ${decrypted}</p>`;
            } catch (error) {
                result.innerHTML = `<p class="error">解密失败: ${error.message}</p>`;
            }
        }

        // AES 测试
        async function testAESEncrypt() {
            const input = document.getElementById('aesInput').value;
            const result = document.getElementById('aesResult');
            
            try {
                await window.loadCryptoLibs();
                if (typeof CryptoJS === 'undefined') {
                    result.innerHTML = '<p class="error">CryptoJS库未加载</p>';
                    return;
                }
                
                const key = "yuemiao_zW4NcAbR";
                const encrypted = CryptoJS.AES.encrypt(input, key).toString();
                result.innerHTML = `<p class="success">加密结果: ${encrypted}</p>`;
            } catch (error) {
                result.innerHTML = `<p class="error">加密失败: ${error.message}</p>`;
            }
        }

        async function testAESDecrypt() {
            const input = document.getElementById('aesInput').value;
            const result = document.getElementById('aesResult');
            
            try {
                await window.loadCryptoLibs();
                if (typeof CryptoJS === 'undefined') {
                    result.innerHTML = '<p class="error">CryptoJS库未加载</p>';
                    return;
                }
                
                const key = "yuemiao_zW4NcAbR";
                const keyParsed = CryptoJS.enc.Utf8.parse(key);
                const bytes = CryptoJS.AES.decrypt(input, keyParsed, {padding: CryptoJS.pad.Pkcs7, mode: CryptoJS.mode.ECB});
                const decrypted = bytes.toString(CryptoJS.enc.Utf8);
                result.innerHTML = `<p class="success">解密结果: ${decrypted}</p>`;
            } catch (error) {
                result.innerHTML = `<p class="error">解密失败: ${error.message}</p>`;
            }
        }

        // 辅助函数
        function strToHex(key) {
            let hexKey = '';
            for (let i = 0; i < key.length; i++) {
                let code = key.charCodeAt(i);
                if (code < 16) hexKey += '0';
                hexKey += code.toString(16).toUpperCase();
            }
            return hexKey;
        }

        function hexStringToString(hexStr) {
            hexStr = hexStr.replace(/\s+/g, '');
            if (hexStr.length % 2 !== 0) {
                throw new Error('Invalid hex string');
            }
            let str = '';
            for (let i = 0; i < hexStr.length; i += 2) {
                const byte = parseInt(hexStr.substr(i, 2), 16);
                if (!isNaN(byte)) {
                    str += String.fromCharCode(byte);
                }
            }
            return str;
        }

        function base64ToHex(base64) {
            const binaryData = atob(base64);
            const len = binaryData.length;
            let bytes = new Uint8Array(len);
            for (let i = 0; i < len; i++) {
                bytes[i] = binaryData.charCodeAt(i);
            }
            let hexString = Array.from(bytes).map(function(byte) {
                return ('0' + (byte & 0xFF).toString(16)).slice(-2).toUpperCase();
            }).join('');
            return hexString;
        }

        // 页面加载完成后检查库状态
        window.addEventListener('load', function() {
            checkLibraries();
        });
    </script>
</body>
</html>
